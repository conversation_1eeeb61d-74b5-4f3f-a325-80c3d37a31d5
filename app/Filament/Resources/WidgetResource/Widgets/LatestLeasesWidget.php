<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Contracts\Support\Htmlable;
use Modules\Lease\app\Models\Lease;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\Enums\LeaseTypesEnum;
use Illuminate\Support\HtmlString;

class LatestLeasesWidget extends BaseWidget
{

    protected function getTableHeading(): string | Htmlable | null
    {
        return __('Latest Leases');
    }

    protected int | string | array $columnSpan = 1;

    public function table(Table $table): Table
    {
        // Get exactly 15 latest leases
        $leases = Lease::query()
            ->latest()
            ->limit(15)
            ->get();

        return $table
            ->query(
                Lease::query()
                    ->with(['property', 'tenant.member'])
                    ->whereIn('id', $leases->pluck('id'))
                    ->latest()
            )
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('ID'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('property.name')
                    ->label(__('Property'))
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                Tables\Columns\TextColumn::make('tenant.member.first_name')
                    ->label(__('Tenant'))
                    ->getStateUsing(function ($record) {
                        $tenant = $record->tenant?->member;
                        if ($tenant) {
                            return $tenant->first_name ;
                        }
                        return '-';
                    })
                    ->limit(25),
                Tables\Columns\TextColumn::make('start_date')
                    ->label(__('Start Date'))
                    ->date('d-m-Y'),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('Status'))
                    ->badge()
                    ->color(fn (string $state): string => LeaseEnum::getColor($state))
                    ->formatStateUsing(fn (string $state): string => LeaseEnum::trans($state)),
                Tables\Columns\TextColumn::make('rent_amount')
                    ->label(__('Rent'))
                    ->formatStateUsing(function ($state) {
                        return new HtmlString(
                            '<div class="">' .
                            number_format($state) .
                            ' <span class="text-xs">' . __('SAR') . '</span>' .
                            '</div>'
                        );
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        'draft' => __('Draft'),
                        'published' => __('Active'),
                        'reserved' => __('Registered'),
                        'near_to_expire' => __('Near To Expire'),
                        'terminated' => __('Terminated'),
                        'terminate_request' => __('Terminate Request'),
                        'closed' => __('Closed'),
                        'close_request' => __('Close Request'),
                        'ended' => __('Expired'),
                        'renewed' => __('Renewed'),
                    ])
                    ->placeholder(__('All Statuses')),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label(__('Details'))
                    ->icon('heroicon-m-eye')
                    ->url(fn (Lease $record): string => \Modules\Lease\app\Filament\Resources\LeaseResource::getUrl('view', ['record' => $record]))
                    ->openUrlInNewTab(),
            ])
            ->headerActions([
                Tables\Actions\Action::make('view_all')
                    ->label(__('View All Leases'))
                    ->icon('heroicon-m-arrow-top-right-on-square')
                    ->url(\Modules\Lease\app\Filament\Resources\LeaseResource::getUrl('index'))
                    ->color('primary'),
            ])
            ->paginated([5, 10, 15])
            ->defaultPaginationPageOption(5);
    }
}
